"""
Listeners pour le mode learn
Gère la configuration et la gestion des listeners pour les différents types de paramètres
"""

import time
from typing import Dict, Any, Callable


def _setup_track_listeners(self, learn_slot: int, track_name: str, track_to_monitor) -> None:
    """Configure les listeners de base pour une piste (nom et couleur)"""
    # Utiliser un dictionnaire pour stocker le dernier timestamp d'envoi pour chaque slot
    if not hasattr(self, '_last_reference_send_time'):
        self._last_reference_send_time = {}

    # Fonction pour éviter les envois multiples trop rapprochés
    def debounced_send_reference():
        current_time = time.time()
        last_time = self._last_reference_send_time.get(learn_slot, 0)

        # Si le dernier envoi était il y a moins de 100ms, ne pas envoyer à nouveau
        if current_time - last_time < 0.1:
            self.logger.debug(f"Ignoring duplicate reference send for slot {learn_slot} (debounce)")
            return

        self._last_reference_send_time[learn_slot] = current_time
        self.send_learn_slot_reference(learn_slot)

    def track_color_changed_callback():
        debounced_send_reference()

    def track_name_changed_callback():
        debounced_send_reference()

    track_to_monitor.add_color_listener(track_color_changed_callback)
    track_to_monitor.add_name_listener(track_name_changed_callback)

    self.learn_listeners[f"learn_{track_name}_color"] = track_color_changed_callback
    self.learn_listeners[f"learn_{track_name}_name"] = track_name_changed_callback


def _setup_volume_listener(self, learn_slot: int, track_name: str, learned_track) -> None:
    """Configure le listener pour le volume"""
    def parameter_changed_callback():
        if not hasattr(parameter_changed_callback, 'is_active') or parameter_changed_callback.is_active:
            value = learned_track.mixer_device.volume.value
            param_id = f"learn_{track_name}_volume"

            def send_value(val):
                self.logger.info(f"Volume changed for {track_name}: {val}")
                self.osc_server.send("/live/tracklearn/get/volume", (learn_slot, val))

            self.parameter_throttler.update_parameter(param_id, value, send_value)

    listener_key = (track_name, "volume")
    learned_track.mixer_device.volume.add_value_listener(parameter_changed_callback)
    self.learn_listeners[listener_key] = parameter_changed_callback
    parameter_changed_callback()


def _setup_pan_listener(self, learn_slot: int, track_name: str, learned_track) -> None:
    """Configure le listener pour le panoramique"""
    def parameter_changed_callback():
        if not hasattr(parameter_changed_callback, 'is_active') or parameter_changed_callback.is_active:
            value = learned_track.mixer_device.panning.value
            param_id = f"learn_{track_name}_panning"

            def send_value(val):
                self.logger.info(f"Panning changed for {track_name}: {val}")
                self.osc_server.send("/live/tracklearn/get/panning", (learn_slot, val))

            self.parameter_throttler.update_parameter(param_id, value, send_value)

    listener_key = (track_name, "panning")
    learned_track.mixer_device.panning.add_value_listener(parameter_changed_callback)
    self.learn_listeners[listener_key] = parameter_changed_callback
    parameter_changed_callback()


def _setup_send_listener(self, learn_slot: int, track_name: str, learned_track, send_index: int) -> None:
    """Configure le listener pour un send"""
    def parameter_changed_callback():
        if not hasattr(parameter_changed_callback, 'is_active') or parameter_changed_callback.is_active:
            value = learned_track.mixer_device.sends[send_index].value
            param_id = f"learn_{track_name}_send_{send_index}"

            def send_value(val):
                self.logger.info(f"Send {send_index} changed for {track_name}: {val}")
                self.osc_server.send("/live/tracklearn/get/sends", (learn_slot, val))

            self.parameter_throttler.update_parameter(param_id, value, send_value)

    listener_key = (track_name, f"send_{send_index}")
    learned_track.mixer_device.sends[send_index].add_value_listener(parameter_changed_callback)
    self.learn_listeners[listener_key] = parameter_changed_callback
    parameter_changed_callback()


def _setup_device_parameter_listener(self, learn_slot: int, track_name: str, learned_device, param_index: int) -> None:
    """Configure le listener pour un paramètre de device"""
    parameter_name = f"device_param_{param_index}"
    parameter_object = learned_device.parameters[param_index]

    # Logs de débogage détaillés
    device_id = id(learned_device)
    param_id_obj = id(parameter_object)
    self.logger.info(f"[DEBUG] Setting up listener for slot {learn_slot}:")
    self.logger.info(f"[DEBUG]   - Device: {learned_device.name} (ID: {device_id})")
    self.logger.info(f"[DEBUG]   - Parameter {param_index}: {parameter_object.name} (ID: {param_id_obj})")
    self.logger.info(f"[DEBUG]   - Listener key: learn_{track_name}_{parameter_name}")

    def create_parameter_callback(slot_number, device, param_idx):
        def parameter_changed_callback():
            if not hasattr(parameter_changed_callback, 'is_active') or parameter_changed_callback.is_active:
                try:
                    value = device.parameters[param_idx].value
                    value_string = device.parameters[param_idx].str_for_value(value)
                    param_id = f"learn_{track_name}_{parameter_name}"

                    def send_value(val):
                        # Logs de débogage pour identifier quel listener se déclenche
                        device_debug_id = id(device)
                        param_debug_id = id(device.parameters[param_idx])
                        self.logger.info(f"[DEBUG] Parameter callback triggered:")
                        self.logger.info(f"[DEBUG]   - Slot: {slot_number}")
                        self.logger.info(f"[DEBUG]   - Device: {device.name} (ID: {device_debug_id})")
                        self.logger.info(f"[DEBUG]   - Parameter {param_idx}: {device.parameters[param_idx].name} (ID: {param_debug_id})")
                        self.logger.info(f"[DEBUG]   - Value: {val} ({value_string})")

                        self.osc_server.send("/live/devicelearn/get/parameter/value",
                                    (slot_number, param_idx, val, value_string))

                    self.parameter_throttler.update_parameter(param_id, value, send_value)

                except Exception as e:
                    self.logger.error(f"Erreur dans parameter_changed_callback: {e}")
        return parameter_changed_callback

    parameter_changed_callback = create_parameter_callback(learn_slot, learned_device, param_index)

    try:
        parameter_object.add_value_listener(parameter_changed_callback)
        listener_key = f"learn_{track_name}_{parameter_name}"
        self.learn_listeners[listener_key] = parameter_changed_callback
        self.logger.info(f"[DEBUG] Listener successfully added with key: {listener_key}")
        self.logger.info(f"[DEBUG] Total listeners now: {len(self.learn_listeners)}")
    except Exception as e:
        self.logger.error(f"Erreur lors de l'ajout du listener: {e}")


def _setup_device_listeners(self, learn_slot: int, track_name: str, learned_device, learned_track) -> None:
    """Configure les listeners supplémentaires pour un device (nom, nom de piste, couleur)"""
    # Utiliser un dictionnaire pour stocker le dernier timestamp d'envoi pour chaque slot
    if not hasattr(self, '_last_reference_send_time'):
        self._last_reference_send_time = {}

    # Fonction pour éviter les envois multiples trop rapprochés
    def debounced_send_reference():
        current_time = time.time()
        last_time = self._last_reference_send_time.get(learn_slot, 0)

        # Si le dernier envoi était il y a moins de 100ms, ne pas envoyer à nouveau
        if current_time - last_time < 0.1:
            self.logger.debug(f"Ignoring duplicate reference send for slot {learn_slot} (debounce)")
            return

        self._last_reference_send_time[learn_slot] = current_time
        self.send_learn_slot_reference(learn_slot)

    def device_name_callback():
        debounced_send_reference()

    def track_name_callback():
        debounced_send_reference()

    def track_color_callback():
        debounced_send_reference()

    # Stocker les callbacks pour pouvoir les réutiliser
    callback_key = f"learn_{track_name}_device_callbacks"

    # Vérifier si des callbacks existent déjà pour ce slot
    if callback_key in self.learn_listeners:
        self.logger.warning(f"Des callbacks device existent déjà pour le slot {learn_slot}, ils seront remplacés")
        # On pourrait ajouter ici du code pour supprimer les anciens listeners si nécessaire

    self.learn_listeners[callback_key] = {
        "device_name": device_name_callback,
        "track_name": track_name_callback,
        "track_color": track_color_callback
    }

    # Ajouter les listeners initiaux
    learned_device.add_name_listener(device_name_callback)
    learned_track.add_name_listener(track_name_callback)
    learned_track.add_color_listener(track_color_callback)

    self.logger.debug(f"Device context listeners ajoutés pour le slot {learn_slot} (device: {learned_device.name}, track: {learned_track.name})")


def _setup_chain_volume_listener(self, learn_slot: int, track_name: str, chain) -> None:
    """Configure le listener pour le volume de la chaîne"""
    def parameter_changed_callback():
        if not hasattr(parameter_changed_callback, 'is_active') or parameter_changed_callback.is_active:
            value = chain.mixer_device.volume.value
            chain_path = self._get_chain_path(chain)
            param_id = f"learn_{track_name}_chain_volume"

            def send_value(val):
                self.logger.info(f"Chain volume changed for {track_name}: {val}, chain_path: {chain_path}")
                # Envoyer uniquement l'index du slot et la valeur, pas le chemin de la chaîne
                self.osc_server.send("/live/chainlearn/get/volume", (learn_slot, val))

            self.parameter_throttler.update_parameter(param_id, value, send_value)

    listener_key = (track_name, "chain_volume")
    chain.mixer_device.volume.add_value_listener(parameter_changed_callback)
    self.learn_listeners[listener_key] = parameter_changed_callback
    parameter_changed_callback()


def _setup_chain_pan_listener(self, learn_slot: int, track_name: str, chain) -> None:
    """Configure le listener pour le panoramique de la chaîne"""
    def parameter_changed_callback():
        if not hasattr(parameter_changed_callback, 'is_active') or parameter_changed_callback.is_active:
            value = chain.mixer_device.panning.value
            chain_path = self._get_chain_path(chain)
            param_id = f"learn_{track_name}_chain_panning"

            def send_value(val):
                self.logger.info(f"Chain panning changed for {track_name}: {val}, chain_path: {chain_path}")
                # Envoyer uniquement l'index du slot et la valeur, pas le chemin de la chaîne
                self.osc_server.send("/live/chainlearn/get/panning", (learn_slot, val))

            self.parameter_throttler.update_parameter(param_id, value, send_value)

    listener_key = (track_name, "chain_panning")
    chain.mixer_device.panning.add_value_listener(parameter_changed_callback)
    self.learn_listeners[listener_key] = parameter_changed_callback
    parameter_changed_callback()


def _setup_chain_listeners(self, learn_slot: int, track_name: str, chain, track) -> None:
    """Configure les listeners pour une chaîne (nom de la chaîne, nom de piste, couleur)"""
    # Utiliser un dictionnaire pour stocker le dernier timestamp d'envoi pour chaque slot
    if not hasattr(self, '_last_reference_send_time'):
        self._last_reference_send_time = {}

    # Fonction pour éviter les envois multiples trop rapprochés
    def debounced_send_reference():
        current_time = time.time()
        last_time = self._last_reference_send_time.get(learn_slot, 0)

        # Si le dernier envoi était il y a moins de 100ms, ne pas envoyer à nouveau
        if current_time - last_time < 0.1:
            self.logger.debug(f"Ignoring duplicate reference send for slot {learn_slot} (debounce)")
            return

        self._last_reference_send_time[learn_slot] = current_time
        self.send_learn_slot_reference(learn_slot)

    def chain_name_callback():
        debounced_send_reference()

    def track_name_callback():
        debounced_send_reference()

    def track_color_callback():
        debounced_send_reference()

    def chains_changed_callback():
        self.logger.info(f"Chains changed in rack for slot {learn_slot}")
        self.verify_learn_slots_validity()

    # Stocker les callbacks
    callback_key = f"learn_{track_name}_chain_callbacks"

    # Vérifier si des callbacks existent déjà pour ce slot
    if callback_key in self.learn_listeners:
        self.logger.warning(f"Des callbacks chain existent déjà pour le slot {learn_slot}, ils seront remplacés")
        # On pourrait ajouter ici du code pour supprimer les anciens listeners si nécessaire

    self.learn_listeners[callback_key] = {
        "chain_name": chain_name_callback,
        "track_name": track_name_callback,
        "track_color": track_color_callback,
        "chains_changed": chains_changed_callback
    }

    # Ajouter les listeners
    chain.add_name_listener(chain_name_callback)
    track.add_name_listener(track_name_callback)
    track.add_color_listener(track_color_callback)
    # Ajouter le listener sur les chaînes du rack parent
    chain.canonical_parent.add_chains_listener(chains_changed_callback)

    self.logger.debug(f"Chain context listeners ajoutés pour le slot {learn_slot} (chain: {chain.name}, track: {track.name})")


def _setup_mute_listener(self, learn_slot: int, track_name: str, learned_track) -> None:
    """Configure le listener pour le mute d'une piste"""
    def mute_changed_callback():
        if not hasattr(mute_changed_callback, 'is_active') or mute_changed_callback.is_active:
            mute_state = 1 if learned_track.mute else 0
            self.logger.info(f"Mute changed for {track_name}: {mute_state}")
            self.osc_server.send("/live/tracklearn/get/mute", (learn_slot, mute_state))

    listener_key = f"track_{learn_slot}_mute"
    learned_track.add_mute_listener(mute_changed_callback)
    self.learn_listeners[listener_key] = mute_changed_callback
    mute_changed_callback()


def _setup_solo_listener(self, learn_slot: int, track_name: str, learned_track) -> None:
    """Configure le listener pour le solo d'une piste"""
    def solo_changed_callback():
        if not hasattr(solo_changed_callback, 'is_active') or solo_changed_callback.is_active:
            solo_state = 1 if learned_track.solo else 0
            self.logger.info(f"Solo changed for {track_name}: {solo_state}")
            self.osc_server.send("/live/tracklearn/get/solo", (learn_slot, solo_state))

    listener_key = f"track_{learn_slot}_solo"
    learned_track.add_solo_listener(solo_changed_callback)
    self.learn_listeners[listener_key] = solo_changed_callback
    solo_changed_callback()


def _setup_chain_mute_listener(self, learn_slot: int, track_name: str, chain) -> None:
    """Configure le listener pour le mute d'une chaîne"""
    def mute_changed_callback():
        if not hasattr(mute_changed_callback, 'is_active') or mute_changed_callback.is_active:
            mute_state = 1 if chain.mute else 0
            chain_path = self._get_chain_path(chain)
            self.logger.info(f"Chain mute changed for {track_name}: {mute_state}, chain_path: {chain_path}")
            self.osc_server.send("/live/chainlearn/get/mute", (learn_slot, mute_state))

    listener_key = (track_name, "chain_mute")
    chain.add_mute_listener(mute_changed_callback)
    self.learn_listeners[listener_key] = mute_changed_callback
    mute_changed_callback()


def _setup_chain_solo_listener(self, learn_slot: int, track_name: str, chain) -> None:
    """Configure le listener pour le solo d'une chaîne"""
    def solo_changed_callback():
        if not hasattr(solo_changed_callback, 'is_active') or solo_changed_callback.is_active:
            solo_state = 1 if chain.solo else 0
            chain_path = self._get_chain_path(chain)
            self.logger.info(f"Chain solo changed for {track_name}: {solo_state}, chain_path: {chain_path}")
            self.osc_server.send("/live/chainlearn/get/solo", (learn_slot, solo_state))

    listener_key = (track_name, "chain_solo")
    chain.add_solo_listener(solo_changed_callback)
    self.learn_listeners[listener_key] = solo_changed_callback
    solo_changed_callback()
