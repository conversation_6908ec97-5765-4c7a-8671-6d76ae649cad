"""
Listeners pour le mode learn
Gère la configuration et la gestion des listeners pour les différents types de paramètres
"""

import time
from typing import Dict, Any, Callable


def _setup_track_listeners(self, learn_slot: int, track_name: str, track_to_monitor) -> None:
    """Configure les listeners de base pour une piste (nom et couleur)"""
    # Utiliser un dictionnaire pour stocker le dernier timestamp d'envoi pour chaque slot
    if not hasattr(self, '_last_reference_send_time'):
        self._last_reference_send_time = {}

    # Fonction pour éviter les envois multiples trop rapprochés
    def debounced_send_reference():
        current_time = time.time()
        last_time = self._last_reference_send_time.get(learn_slot, 0)

        # Si le dernier envoi était il y a moins de 100ms, ne pas envoyer à nouveau
        if current_time - last_time < 0.1:
            self.logger.debug(f"Ignoring duplicate reference send for slot {learn_slot} (debounce)")
            return

        self._last_reference_send_time[learn_slot] = current_time
        self.send_learn_slot_reference(learn_slot)

    def track_color_changed_callback():
        debounced_send_reference()

    def track_name_changed_callback():
        debounced_send_reference()

    track_to_monitor.add_color_listener(track_color_changed_callback)
    track_to_monitor.add_name_listener(track_name_changed_callback)

    self.learn_listeners[f"learn_{track_name}_color"] = track_color_changed_callback
    self.learn_listeners[f"learn_{track_name}_name"] = track_name_changed_callback


def _setup_volume_listener(self, learn_slot: int, track_name: str, learned_track) -> None:
    """Configure le listener pour le volume"""
    def parameter_changed_callback():
        if not hasattr(parameter_changed_callback, 'is_active') or parameter_changed_callback.is_active:
            value = learned_track.mixer_device.volume.value
            param_id = f"learn_{track_name}_volume"

            def send_value(val):
                self.logger.info(f"Volume changed for {track_name}: {val}")
                self.osc_server.send("/live/tracklearn/get/volume", (learn_slot, val))

            self.parameter_throttler.update_parameter(param_id, value, send_value)

    listener_key = (track_name, "volume")
    learned_track.mixer_device.volume.add_value_listener(parameter_changed_callback)
    self.learn_listeners[listener_key] = parameter_changed_callback
    parameter_changed_callback()


def _setup_pan_listener(self, learn_slot: int, track_name: str, learned_track) -> None:
    """Configure le listener pour le panoramique"""
    def parameter_changed_callback():
        if not hasattr(parameter_changed_callback, 'is_active') or parameter_changed_callback.is_active:
            value = learned_track.mixer_device.panning.value
            param_id = f"learn_{track_name}_panning"

            def send_value(val):
                self.logger.info(f"Panning changed for {track_name}: {val}")
                self.osc_server.send("/live/tracklearn/get/panning", (learn_slot, val))

            self.parameter_throttler.update_parameter(param_id, value, send_value)

    listener_key = (track_name, "panning")
    learned_track.mixer_device.panning.add_value_listener(parameter_changed_callback)
    self.learn_listeners[listener_key] = parameter_changed_callback
    parameter_changed_callback()


def _setup_send_listener(self, learn_slot: int, track_name: str, learned_track, send_index: int) -> None:
    """Configure le listener pour un send"""
    def parameter_changed_callback():
        if not hasattr(parameter_changed_callback, 'is_active') or parameter_changed_callback.is_active:
            value = learned_track.mixer_device.sends[send_index].value
            param_id = f"learn_{track_name}_send_{send_index}"

            def send_value(val):
                self.logger.info(f"Send {send_index} changed for {track_name}: {val}")
                self.osc_server.send("/live/tracklearn/get/sends", (learn_slot, val))

            self.parameter_throttler.update_parameter(param_id, value, send_value)

    listener_key = (track_name, f"send_{send_index}")
    learned_track.mixer_device.sends[send_index].add_value_listener(parameter_changed_callback)
    self.learn_listeners[listener_key] = parameter_changed_callback
    parameter_changed_callback()


def _setup_device_parameter_listener(self, learn_slot: int, track_name: str, learned_device, param_index: int) -> None:
    """Configure le listener pour un paramètre de device avec filtrage strict"""
    parameter_name = f"device_param_{param_index}"
    parameter_object = learned_device.parameters[param_index]

    # Créer un identifiant unique pour ce slot/device/paramètre
    device_signature = f"{learned_device.name}_{id(learned_device)}_{param_index}"

    # Initialiser le registre des signatures si nécessaire
    if not hasattr(self, '_device_param_signatures'):
        self._device_param_signatures = {}

    # Enregistrer la signature pour ce slot
    self._device_param_signatures[learn_slot] = device_signature

    self.logger.info(f"[DEBUG] Setting up STRICT listener for slot {learn_slot}:")
    self.logger.info(f"[DEBUG]   - Device: {learned_device.name} (ID: {id(learned_device)})")
    self.logger.info(f"[DEBUG]   - Parameter {param_index}: {parameter_object.name}")
    self.logger.info(f"[DEBUG]   - Signature: {device_signature}")

    # Nettoyer d'abord tous les listeners existants pour ce slot
    self._cleanup_device_listeners_for_slot(learn_slot, track_name)

    def create_strict_parameter_callback(slot_number, device, param_idx, param_obj, expected_signature):
        def parameter_changed_callback():
            if not hasattr(parameter_changed_callback, 'is_active') or parameter_changed_callback.is_active:
                try:
                    # FILTRAGE STRICT : Vérifier que ce callback correspond bien à ce slot
                    current_signature = f"{device.name}_{id(device)}_{param_idx}"

                    # Vérifier dans le registre des signatures
                    registered_signature = self._device_param_signatures.get(slot_number)

                    if current_signature != expected_signature or current_signature != registered_signature:
                        self.logger.debug(f"[FILTER] Ignoring callback for slot {slot_number}:")
                        self.logger.debug(f"[FILTER]   - Current: {current_signature}")
                        self.logger.debug(f"[FILTER]   - Expected: {expected_signature}")
                        self.logger.debug(f"[FILTER]   - Registered: {registered_signature}")
                        return

                    # Vérification supplémentaire : le slot doit encore exister et être actif
                    if slot_number not in self.learn_slots or not self.learn_slots[slot_number].get("track"):
                        self.logger.debug(f"[FILTER] Slot {slot_number} no longer active, ignoring callback")
                        return

                    value = param_obj.value
                    value_string = param_obj.str_for_value(value)
                    param_id = f"learn_{track_name}_{parameter_name}"

                    def send_value(val):
                        self.logger.info(f"[VALID] Parameter {param_idx} changed for device in slot {slot_number}: {val} ({value_string})")
                        self.osc_server.send("/live/devicelearn/get/parameter/value",
                                    (slot_number, param_idx, val, value_string))

                    self.parameter_throttler.update_parameter(param_id, value, send_value)

                except Exception as e:
                    self.logger.error(f"Erreur dans parameter_changed_callback: {e}")
        return parameter_changed_callback

    parameter_changed_callback = create_strict_parameter_callback(
        learn_slot, learned_device, param_index, parameter_object, device_signature)

    try:
        parameter_object.add_value_listener(parameter_changed_callback)
        listener_key = f"learn_{track_name}_{parameter_name}"
        self.learn_listeners[listener_key] = {
            'callback': parameter_changed_callback,
            'parameter': parameter_object,
            'signature': device_signature,
            'slot': learn_slot
        }

        self.logger.info(f"[DEBUG] STRICT listener successfully added for slot {learn_slot}")
        self.logger.info(f"[DEBUG] Total listeners now: {len(self.learn_listeners)}")
    except Exception as e:
        self.logger.error(f"Erreur lors de l'ajout du listener: {e}")


def _cleanup_device_listeners_for_slot(self, slot_number: int, track_name: str) -> None:
    """Nettoie tous les listeners de device pour un slot spécifique"""
    keys_to_remove = []

    for key, listener_data in self.learn_listeners.items():
        if key.startswith(f"learn_{track_name}_device_param_"):
            if isinstance(listener_data, dict) and listener_data.get('slot') == slot_number:
                keys_to_remove.append(key)

    for key in keys_to_remove:
        try:
            listener_data = self.learn_listeners[key]
            if isinstance(listener_data, dict):
                param_obj = listener_data['parameter']
                callback = listener_data['callback']
                param_obj.remove_value_listener(callback)
            else:
                # Ancienne structure, essayer de nettoyer quand même
                self.logger.warning(f"Old listener structure found for key: {key}")

            del self.learn_listeners[key]
            self.logger.info(f"[DEBUG] Cleaned up listener: {key}")
        except Exception as e:
            self.logger.error(f"Error cleaning up listener {key}: {e}")


def _setup_device_listeners(self, learn_slot: int, track_name: str, learned_device, learned_track) -> None:
    """Configure les listeners supplémentaires pour un device (nom, nom de piste, couleur)"""
    # Utiliser un dictionnaire pour stocker le dernier timestamp d'envoi pour chaque slot
    if not hasattr(self, '_last_reference_send_time'):
        self._last_reference_send_time = {}

    # Fonction pour éviter les envois multiples trop rapprochés
    def debounced_send_reference():
        current_time = time.time()
        last_time = self._last_reference_send_time.get(learn_slot, 0)

        # Si le dernier envoi était il y a moins de 100ms, ne pas envoyer à nouveau
        if current_time - last_time < 0.1:
            self.logger.debug(f"Ignoring duplicate reference send for slot {learn_slot} (debounce)")
            return

        self._last_reference_send_time[learn_slot] = current_time
        self.send_learn_slot_reference(learn_slot)

    def device_name_callback():
        debounced_send_reference()

    def track_name_callback():
        debounced_send_reference()

    def track_color_callback():
        debounced_send_reference()

    # Stocker les callbacks pour pouvoir les réutiliser
    callback_key = f"learn_{track_name}_device_callbacks"

    # Vérifier si des callbacks existent déjà pour ce slot
    if callback_key in self.learn_listeners:
        self.logger.warning(f"Des callbacks device existent déjà pour le slot {learn_slot}, ils seront remplacés")
        # On pourrait ajouter ici du code pour supprimer les anciens listeners si nécessaire

    self.learn_listeners[callback_key] = {
        "device_name": device_name_callback,
        "track_name": track_name_callback,
        "track_color": track_color_callback
    }

    # Ajouter les listeners initiaux
    learned_device.add_name_listener(device_name_callback)
    learned_track.add_name_listener(track_name_callback)
    learned_track.add_color_listener(track_color_callback)

    self.logger.debug(f"Device context listeners ajoutés pour le slot {learn_slot} (device: {learned_device.name}, track: {learned_track.name})")


def _setup_chain_volume_listener(self, learn_slot: int, track_name: str, chain) -> None:
    """Configure le listener pour le volume de la chaîne"""
    def parameter_changed_callback():
        if not hasattr(parameter_changed_callback, 'is_active') or parameter_changed_callback.is_active:
            value = chain.mixer_device.volume.value
            chain_path = self._get_chain_path(chain)
            param_id = f"learn_{track_name}_chain_volume"

            def send_value(val):
                self.logger.info(f"Chain volume changed for {track_name}: {val}, chain_path: {chain_path}")
                # Envoyer uniquement l'index du slot et la valeur, pas le chemin de la chaîne
                self.osc_server.send("/live/chainlearn/get/volume", (learn_slot, val))

            self.parameter_throttler.update_parameter(param_id, value, send_value)

    listener_key = (track_name, "chain_volume")
    chain.mixer_device.volume.add_value_listener(parameter_changed_callback)
    self.learn_listeners[listener_key] = parameter_changed_callback
    parameter_changed_callback()


def _setup_chain_pan_listener(self, learn_slot: int, track_name: str, chain) -> None:
    """Configure le listener pour le panoramique de la chaîne"""
    def parameter_changed_callback():
        if not hasattr(parameter_changed_callback, 'is_active') or parameter_changed_callback.is_active:
            value = chain.mixer_device.panning.value
            chain_path = self._get_chain_path(chain)
            param_id = f"learn_{track_name}_chain_panning"

            def send_value(val):
                self.logger.info(f"Chain panning changed for {track_name}: {val}, chain_path: {chain_path}")
                # Envoyer uniquement l'index du slot et la valeur, pas le chemin de la chaîne
                self.osc_server.send("/live/chainlearn/get/panning", (learn_slot, val))

            self.parameter_throttler.update_parameter(param_id, value, send_value)

    listener_key = (track_name, "chain_panning")
    chain.mixer_device.panning.add_value_listener(parameter_changed_callback)
    self.learn_listeners[listener_key] = parameter_changed_callback
    parameter_changed_callback()


def _setup_chain_listeners(self, learn_slot: int, track_name: str, chain, track) -> None:
    """Configure les listeners pour une chaîne (nom de la chaîne, nom de piste, couleur)"""
    # Utiliser un dictionnaire pour stocker le dernier timestamp d'envoi pour chaque slot
    if not hasattr(self, '_last_reference_send_time'):
        self._last_reference_send_time = {}

    # Fonction pour éviter les envois multiples trop rapprochés
    def debounced_send_reference():
        current_time = time.time()
        last_time = self._last_reference_send_time.get(learn_slot, 0)

        # Si le dernier envoi était il y a moins de 100ms, ne pas envoyer à nouveau
        if current_time - last_time < 0.1:
            self.logger.debug(f"Ignoring duplicate reference send for slot {learn_slot} (debounce)")
            return

        self._last_reference_send_time[learn_slot] = current_time
        self.send_learn_slot_reference(learn_slot)

    def chain_name_callback():
        debounced_send_reference()

    def track_name_callback():
        debounced_send_reference()

    def track_color_callback():
        debounced_send_reference()

    def chains_changed_callback():
        self.logger.info(f"Chains changed in rack for slot {learn_slot}")
        self.verify_learn_slots_validity()

    # Stocker les callbacks
    callback_key = f"learn_{track_name}_chain_callbacks"

    # Vérifier si des callbacks existent déjà pour ce slot
    if callback_key in self.learn_listeners:
        self.logger.warning(f"Des callbacks chain existent déjà pour le slot {learn_slot}, ils seront remplacés")
        # On pourrait ajouter ici du code pour supprimer les anciens listeners si nécessaire

    self.learn_listeners[callback_key] = {
        "chain_name": chain_name_callback,
        "track_name": track_name_callback,
        "track_color": track_color_callback,
        "chains_changed": chains_changed_callback
    }

    # Ajouter les listeners
    chain.add_name_listener(chain_name_callback)
    track.add_name_listener(track_name_callback)
    track.add_color_listener(track_color_callback)
    # Ajouter le listener sur les chaînes du rack parent
    chain.canonical_parent.add_chains_listener(chains_changed_callback)

    self.logger.debug(f"Chain context listeners ajoutés pour le slot {learn_slot} (chain: {chain.name}, track: {track.name})")


def _setup_mute_listener(self, learn_slot: int, track_name: str, learned_track) -> None:
    """Configure le listener pour le mute d'une piste"""
    def mute_changed_callback():
        if not hasattr(mute_changed_callback, 'is_active') or mute_changed_callback.is_active:
            mute_state = 1 if learned_track.mute else 0
            self.logger.info(f"Mute changed for {track_name}: {mute_state}")
            self.osc_server.send("/live/tracklearn/get/mute", (learn_slot, mute_state))

    listener_key = f"track_{learn_slot}_mute"
    learned_track.add_mute_listener(mute_changed_callback)
    self.learn_listeners[listener_key] = mute_changed_callback
    mute_changed_callback()


def _setup_solo_listener(self, learn_slot: int, track_name: str, learned_track) -> None:
    """Configure le listener pour le solo d'une piste"""
    def solo_changed_callback():
        if not hasattr(solo_changed_callback, 'is_active') or solo_changed_callback.is_active:
            solo_state = 1 if learned_track.solo else 0
            self.logger.info(f"Solo changed for {track_name}: {solo_state}")
            self.osc_server.send("/live/tracklearn/get/solo", (learn_slot, solo_state))

    listener_key = f"track_{learn_slot}_solo"
    learned_track.add_solo_listener(solo_changed_callback)
    self.learn_listeners[listener_key] = solo_changed_callback
    solo_changed_callback()


def _setup_chain_mute_listener(self, learn_slot: int, track_name: str, chain) -> None:
    """Configure le listener pour le mute d'une chaîne"""
    def mute_changed_callback():
        if not hasattr(mute_changed_callback, 'is_active') or mute_changed_callback.is_active:
            mute_state = 1 if chain.mute else 0
            chain_path = self._get_chain_path(chain)
            self.logger.info(f"Chain mute changed for {track_name}: {mute_state}, chain_path: {chain_path}")
            self.osc_server.send("/live/chainlearn/get/mute", (learn_slot, mute_state))

    listener_key = (track_name, "chain_mute")
    chain.add_mute_listener(mute_changed_callback)
    self.learn_listeners[listener_key] = mute_changed_callback
    mute_changed_callback()


def _setup_chain_solo_listener(self, learn_slot: int, track_name: str, chain) -> None:
    """Configure le listener pour le solo d'une chaîne"""
    def solo_changed_callback():
        if not hasattr(solo_changed_callback, 'is_active') or solo_changed_callback.is_active:
            solo_state = 1 if chain.solo else 0
            chain_path = self._get_chain_path(chain)
            self.logger.info(f"Chain solo changed for {track_name}: {solo_state}, chain_path: {chain_path}")
            self.osc_server.send("/live/chainlearn/get/solo", (learn_slot, solo_state))

    listener_key = (track_name, "chain_solo")
    chain.add_solo_listener(solo_changed_callback)
    self.learn_listeners[listener_key] = solo_changed_callback
    solo_changed_callback()
