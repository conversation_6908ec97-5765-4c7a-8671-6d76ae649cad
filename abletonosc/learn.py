from .handler import <PERSON>ble<PERSON><PERSON><PERSON><PERSON><PERSON>
from .utils.parameter_throttler import ParameterThrottler

# Import des handlers et utilitaires pour le mode learn
from .learn_mode_helper.learn_mode_encoder_handlers import (
    adjust_learn_parameter,
    _adjust_track_volume,
    _adjust_track_pan,
    _adjust_track_send,
    _adjust_device_parameter,
    _adjust_chain_volume,
    _adjust_chain_pan,
    _adjust_track_mute,
    _adjust_track_solo,
    _adjust_chain_mute,
    _adjust_chain_solo,
    _adjust_quantized_parameter,
    _is_device_rack,
    _is_parameter_quantized
)
from .learn_mode_helper.learn_mode_listeners import (
    _setup_track_listeners,
    _setup_volume_listener,
    _setup_pan_listener,
    _setup_send_listener,
    _setup_device_parameter_listener,
    _setup_device_listeners,
    _setup_chain_volume_listener,
    _setup_chain_pan_listener,
    _setup_chain_listeners,
    _setup_chain_mute_listener,
    _setup_chain_solo_listener,
    _setup_mute_listener,
    _setup_solo_listener,
    _cleanup_device_listeners_for_slot
)
from .learn_mode_helper.learn_mode_utils import (
    _get_chain_path,
    _get_chain_by_path,
    _find_device_or_chain_track
)
from .learn_mode_helper.learn_mode_slot_management import (
    _check_duplicates,
    delete_single_slot,
    verify_learn_slots_validity,
    send_learn_slot_reference
)
from .learn_mode_helper.learn_mode_osc_actions import (
    start_learn_listen,
    select_learn_slot_element,
    stop_learn_listen
)
from .learn_mode_helper.learn_mode_value_actions import (
    set_learn_slot_value
)

class LearnModeHandler(AbletonOSCHandler):
    def __init__(self, manager):
        super().__init__(manager)
        self.class_identifier = "learn"
        self.learn_slots = {i: {
            "track": None,
            "device": None,
            "param_type": None,
            "param_index": None
        } for i in range(32)}
        self.learn_listeners = {}
        self.parameter_throttler = ParameterThrottler()

        # Buffers pour les paramètres quantifiés (similaire au mode device)
        self.quantized_param_buffers = {}

    # Intégration des méthodes importées
    adjust_learn_parameter = adjust_learn_parameter

    # Méthodes d'ajustement des paramètres
    _adjust_track_volume = _adjust_track_volume
    _adjust_track_pan = _adjust_track_pan
    _adjust_track_send = _adjust_track_send
    _adjust_device_parameter = _adjust_device_parameter
    _adjust_chain_volume = _adjust_chain_volume
    _adjust_chain_pan = _adjust_chain_pan
    _adjust_track_mute = _adjust_track_mute
    _adjust_track_solo = _adjust_track_solo
    _adjust_chain_mute = _adjust_chain_mute
    _adjust_chain_solo = _adjust_chain_solo
    _adjust_quantized_parameter = _adjust_quantized_parameter
    _is_device_rack = _is_device_rack
    _is_parameter_quantized = _is_parameter_quantized

    # Listeners
    _setup_track_listeners = _setup_track_listeners
    _setup_volume_listener = _setup_volume_listener
    _setup_pan_listener = _setup_pan_listener
    _setup_send_listener = _setup_send_listener
    _setup_device_parameter_listener = _setup_device_parameter_listener
    _setup_device_listeners = _setup_device_listeners
    _setup_chain_volume_listener = _setup_chain_volume_listener
    _setup_chain_pan_listener = _setup_chain_pan_listener
    _setup_chain_listeners = _setup_chain_listeners
    _setup_chain_mute_listener = _setup_chain_mute_listener
    _setup_chain_solo_listener = _setup_chain_solo_listener
    _setup_mute_listener = _setup_mute_listener
    _setup_solo_listener = _setup_solo_listener
    _cleanup_device_listeners_for_slot = _cleanup_device_listeners_for_slot

    # Utilitaires
    _get_chain_path = _get_chain_path
    _get_chain_by_path = _get_chain_by_path
    _find_device_or_chain_track = _find_device_or_chain_track

    # Gestion des slots
    _check_duplicates = _check_duplicates
    delete_single_slot = delete_single_slot
    verify_learn_slots_validity = verify_learn_slots_validity
    send_learn_slot_reference = send_learn_slot_reference

    # Actions OSC
    start_learn_listen = start_learn_listen
    select_learn_slot_element = select_learn_slot_element
    stop_learn_listen = stop_learn_listen
    set_learn_slot_value = set_learn_slot_value

    def init_api(self):
        # Enregistrement des handlers pour le learn mode
        self.osc_server.add_handler("/live/learn/set/value", self.set_learn_slot_value)
        self.osc_server.add_handler("/live/learn/stop", lambda _: self.stop_learn_listen())
        self.osc_server.add_handler("/live/learn/slot", self.start_learn_listen)
        self.osc_server.add_handler("/live/learn/select", self.select_learn_slot_element)
        self.osc_server.add_handler("/live/learn/del_slot", self.delete_single_slot)
        self.osc_server.add_handler("/live/learn/set", self.set_learn_slot_value)

        # Handler pour les encoders du mode learn
        self.osc_server.add_handler("/live/learn/adjust/parameter",
            lambda params: self.adjust_learn_parameter(params))










